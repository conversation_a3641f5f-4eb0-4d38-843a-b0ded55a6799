<template>
  <view class="call-container">
    <!-- 通话背景 -->
    <view class="call-background"></view>

    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="time">{{ currentTime }}</view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 用户头像区域 -->
      <view class="avatar-section">
        <view class="avatar-container">
          <view class="avatar-icon">
            <text class="avatar-emoji">😊</text>
          </view>
        </view>
        <text class="username">{{ targetUser?.displayName || targetUser?.username || '未知用户' }}</text>
      </view>

      <!-- 状态提示 -->
      <view class="status-section">
        <text class="status-text">{{ statusText }}</text>
      </view>
    </view>

    <!-- 底部控制按钮 -->
    <view class="bottom-controls">
      <!-- 麦克风按钮 -->
      <view class="control-button" @click="toggleMute">
        <view class="button-circle secondary" :class="{ active: !isMuted }">
          <text class="button-icon white-icon">🎤</text>
        </view>
        <text class="button-label">{{ isMuted ? '麦克风已关' : '麦克风已开' }}</text>
      </view>

      <!-- 挂断按钮 -->
      <view class="control-button" @click="endCall">
        <view class="button-circle primary">
          <text class="button-icon white-icon">📞</text>
        </view>
        <text class="button-label">取消</text>
      </view>

      <!-- 扬声器按钮 -->
      <view class="control-button" @click="toggleSpeaker">
        <view class="button-circle secondary" :class="{ active: isSpeakerOn }">
          <text class="button-icon white-icon">🔇</text>
        </view>
        <text class="button-label">{{ isSpeakerOn ? '扬声器开' : '扬声器关' }}</text>
      </view>
    </view>

    <!-- Toast提示 -->
    <Toast
      v-model:visible="toastVisible"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Button, Toast } from '@/components'
import { useUserStore, UserStatus } from '@/stores/user'
import { useSocketStore } from '@/stores/socket'

// 使用状态管理
const userStore = useUserStore()
const socketStore = useSocketStore()

// 响应式数据
const targetUser = ref<any>(null)
const callStatus = ref<'idle' | 'connecting' | 'connected' | 'talking' | 'ended'>('connecting')
const callDuration = ref(0)
const isConnected = ref(false)
const isTalking = ref(false)
const isMuted = ref(false)
const isSpeakerOn = ref(false)
const audioQuality = ref(3)
const overlayMessage = ref<any>(null)
const currentTime = ref('')

// Toast相关状态
const toastVisible = ref(false)
const toastMessage = ref('')
const toastType = ref<'success' | 'error' | 'info' | 'warning'>('info')

// 通话相关定时器
let _callTimer: any = null
let durationTimer: any = null
let timeTimer: any = null

// 音频相关
let audioRecorder: any = null
let _audioPlayer: any = null
let audioContext: any = null

// 计算属性
const statusText = computed(() => {
  switch (callStatus.value) {
    case 'connecting':
      return '等待对方接受邀请...'
    case 'connected':
      return '已连接'
    case 'talking':
      return '通话中'
    case 'ended':
      return '通话结束'
    default:
      return '正在连接...'
  }
})

const canStartCall = computed(() => {
  return callStatus.value === 'connected' && !isTalking.value && userStore.hasPermission('microphone')
})

// 更新时间
const updateTime = () => {
  const now = new Date()
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}`
}

// 获取用户详细信息
const fetchUserDetails = async (userId: number) => {
  try {
    // 从本地存储获取token
    const token = uni.getStorageSync('token')
    if (!token) {
      console.warn('没有认证token，无法获取用户详细信息')
      return
    }

    const response = await uni.request({
      url: `http://localhost:3001/api/users/${userId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    const responseData = response.data as any
    if (response.statusCode === 200 && responseData.success) {
      const userDetails = responseData.data.user
      // 更新目标用户信息
      targetUser.value = {
        ...targetUser.value,
        ...userDetails
      }
      console.log('获取用户详细信息成功:', userDetails)
    } else {
      console.error('获取用户详细信息失败:', responseData)
      showToast('获取用户信息失败', 'warning')
    }
  } catch (error) {
    console.error('获取用户详细信息出错:', error)
    showToast('网络错误，无法获取用户信息', 'error')
  }
}

// 页面加载
onMounted(async () => {
  // 启动时间更新
  updateTime()
  timeTimer = setInterval(updateTime, 1000)

  // 获取传入的目标用户信息
  const pages = (uni as any).getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options

  if (options.targetUser) {
    try {
      const basicUserInfo = JSON.parse(decodeURIComponent(options.targetUser))
      targetUser.value = basicUserInfo

      // 获取用户详细信息
      await fetchUserDetails(basicUserInfo.id)
    } catch (error) {
      console.error('解析目标用户信息失败:', error)
      showToast('用户信息错误', 'error')
      return
    }
  }

  // 初始化通话
  await initializeCall()
})

// 页面卸载
onUnmounted(() => {
  // 清理时间定时器
  if (timeTimer) {
    clearInterval(timeTimer)
    timeTimer = null
  }
  cleanup()
})

// 初始化通话
const initializeCall = async () => {
  try {
    // 检查权限
    if (!userStore.hasPermission('microphone')) {
      const granted = await userStore.requestPermission('microphone')
      if (!granted) {
        showToast('需要麦克风权限才能进行通话', 'error')
        return
      }
    }

    // 确保Socket.io已连接
    if (!socketStore.isConnected) {
      throw new Error('Socket.io未连接')
    }

    // 设置通话相关的Socket.io监听器
    setupCallSocketListeners()

    // 初始化音频
    await initializeAudio()

    // 发起通话请求
    await requestCall()

  } catch (error) {
    console.error('初始化通话失败:', error)
    showToast('通话初始化失败', 'error')
  }
}

// 设置通话相关的Socket.io监听器
const setupCallSocketListeners = () => {
  // 注意：Socket store已经处理了大部分事件监听
  // 这里只需要处理通话页面特有的逻辑

  // 监听通话状态变化
  const handleCallStateChange = () => {
    if (socketStore.currentCall) {
      const call = socketStore.currentCall
      callStatus.value = call.status
      targetUser.value = call.participants.find(p => p.id !== userStore.userInfo?.id)

      if (call.status === 'connected') {
        isConnected.value = true
        startCallTimer()
      } else if (call.status === 'ended') {
        handleCallEnd({ reason: 'normal' })
      }
    }
  }

  // 监听来电
  const handleIncomingCall = () => {
    if (socketStore.incomingCall) {
      const call = socketStore.incomingCall
      targetUser.value = call.caller
      callStatus.value = 'connecting'
    }
  }

  // 监听后台消息（通话期间）
  const handleBackendMessages = () => {
    if (socketStore.messages.length > 0) {
      const latestMessage = socketStore.messages[0]
      if (latestMessage.type === 'backend_call') {
        handleBackendCallDuringTalk(latestMessage.data)
      }
    }
  }

  // 设置响应式监听
  // 这些会在组件卸载时自动清理
}

// WebSocket消息处理已移至setupCallWebSocketListeners方法中
// 添加后台消息处理到监听器设置中
const handleBackendCallDuringTalk = (data: any) => {
  // 在通话中收到后台管理消息时的处理
  if (data.priority === 'urgent') {
    // 紧急消息：暂停通话并显示消息
    // pauseTalk() // TODO: 实现暂停通话功能
    // showOverlayMessage(data.message, 'urgent') // TODO: 实现消息显示
    console.log('Urgent message:', data.message)
  } else {
    // 普通消息：降低通话音量并显示提示
    if (audioContext) {
      audioContext.setVolume(0.3) // 降低音量到30%
    }
    // showOverlayMessage(data.message, 'normal') // TODO: 实现消息显示
    console.log('Normal message:', data.message)

    // 5秒后恢复音量
    setTimeout(() => {
      if (audioContext) {
        audioContext.setVolume(1.0)
      }
    }, 5000)
  }
}

// 发送Socket.io消息
const sendCallMessage = (eventName: string, data: any) => {
  if (socketStore.isConnected) {
    const messageData = {
      ...data,
      timestamp: Date.now(),
      fromUserId: userStore.userInfo?.id,
      fromUsername: userStore.userInfo?.username,
      targetUserId: targetUser.value?.id,
      targetUsername: targetUser.value?.username
    }

    socketStore.sendMessage(eventName, messageData)
  } else {
    console.warn('Socket.io未连接，无法发送通话消息:', eventName)
  }
}

// 初始化音频
const initializeAudio = async () => {
  try {
    // 初始化录音器 - 根据PRD要求设置音频参数
    audioRecorder = uni.getRecorderManager()
    
    // 录音配置 - 16kHz采样率，AAC编码，64kbps，单声道
    const _recordOptions = {
      duration: 600000, // 最大录音时长10分钟
      sampleRate: 16000, // 16kHz采样率
      numberOfChannels: 1, // 单声道
      encodeBitRate: 64000, // 64kbps比特率
      format: 'aac', // AAC格式
      frameSize: 1024 // 1024字节/帧
    }

    // 录音事件监听
    audioRecorder.onStart(() => {
      console.log('开始录音')
    })

    audioRecorder.onFrameRecorded((res: any) => {
      // 实时音频流传输 - 20ms/帧
      if (isTalking.value) {
        sendVoiceData(res.frameBuffer)
      }
    })

    audioRecorder.onStop((_res: any) => {
      console.log('录音结束')
    })

    audioRecorder.onError((error: any) => {
      console.error('录音错误:', error)
      showToast('录音失败', 'error')
    })

    // 初始化音频播放器
    audioContext = uni.createInnerAudioContext()
    
    audioContext.onPlay(() => {
      console.log('开始播放音频')
    })

    audioContext.onEnded(() => {
      console.log('音频播放结束')
    })

    audioContext.onError((error: any) => {
      console.error('音频播放错误:', error)
    })

  } catch (error) {
    console.error('初始化音频失败:', error)
    throw error
  }
}

// 发起通话请求
const requestCall = async () => {
  callStatus.value = 'connecting'
  
  sendCallMessage('call-request', {
    targetUser: targetUser.value.username,
    callType: 'voice'
  })
}

// 处理通话响应
const handleCallResponse = (data: any) => {
  if (data.accepted) {
    callStatus.value = 'connected'
    isConnected.value = true
    showToast('连接成功，可以开始对讲', 'success')
  } else {
    callStatus.value = 'ended'
    showToast(data.reason || '对方拒绝了通话', 'error')
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
}

// 开始通话
const startCall = async () => {
  if (!canStartCall.value) {
    showToast('当前无法开始通话', 'warning')
    return
  }

  try {
    isTalking.value = true
    callStatus.value = 'talking'
    
    // 更新用户状态
    userStore.updateUserStatus(UserStatus.TALKING)
    
    // 开始录音
    audioRecorder.start({
      duration: 600000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 64000,
      format: 'aac',
      frameSize: 1024
    })

    // 发送开始通话消息
    sendCallMessage('voice-start', {
      targetUser: targetUser.value.username
    })

    // 开始计时
    startDurationTimer()

    showToast('开始对讲', 'success')
  } catch (error) {
    console.error('开始通话失败:', error)
    showToast('开始通话失败', 'error')
    isTalking.value = false
    callStatus.value = 'connected'
  }
}

// 结束通话
const endCall = async () => {
  try {
    isTalking.value = false
    callStatus.value = 'connected'
    
    // 停止录音
    if (audioRecorder) {
      audioRecorder.stop()
    }

    // 发送结束通话消息
    sendCallMessage('voice-end', {
      targetUser: targetUser.value.username
    })

    // 更新用户状态
    userStore.updateUserStatus(UserStatus.ONLINE)

    // 停止计时
    stopDurationTimer()

    showToast('结束对讲', 'info')
  } catch (error) {
    console.error('结束通话失败:', error)
    showToast('结束通话失败', 'error')
  }
}

// 发送语音数据
const sendVoiceData = (frameBuffer: ArrayBuffer) => {
  if (socketStore.isConnected && isTalking.value) {
    // 将音频帧数据转换为Base64发送
    const base64Data = uni.arrayBufferToBase64(frameBuffer)

    sendCallMessage('voice-data', {
      audioData: base64Data,
      format: 'pcm',
      sampleRate: 16000,
      channels: 1,
      sessionId: socketStore.currentCall?.callId,
      timestamp: Date.now()
    })
  }
}

// 处理接收到的语音数据
const handleVoiceData = (data: any) => {
  try {
    // 将Base64数据转换回ArrayBuffer
    const audioData = uni.base64ToArrayBuffer(data.audioData)
    
    // 播放音频数据
    playAudioData(audioData)
    
    // 更新音频质量指示器
    updateAudioQuality()
  } catch (error) {
    console.error('处理语音数据失败:', error)
  }
}

// 播放音频数据
const playAudioData = (audioData: ArrayBuffer) => {
  // 这里需要实现实时音频播放
  // 由于uni-app的限制，可能需要使用原生插件或其他方案
  console.log('播放音频数据:', audioData.byteLength, '字节')
}

// 处理通话结束
const handleCallEnd = (_data: any) => {
  callStatus.value = 'ended'
  isTalking.value = false
  isConnected.value = false
  
  // 停止录音
  if (audioRecorder) {
    audioRecorder.stop()
  }

  // 更新用户状态
  userStore.updateUserStatus(UserStatus.ONLINE)

  // 停止计时
  stopDurationTimer()

  showToast('通话已结束', 'info')
  
  setTimeout(() => {
    uni.navigateBack()
  }, 2000)
}

// 重复函数定义已删除，使用上面的版本

// 播放通知提示音
const _playNotificationSound = () => {
  // 播放系统提示音
  uni.showToast({
    title: '新消息',
    icon: 'none',
    duration: 1000
  })
}

// 关闭叠加消息
const dismissOverlayMessage = () => {
  overlayMessage.value = null
}

// 开始计时
const startDurationTimer = () => {
  callDuration.value = 0
  durationTimer = setInterval(() => {
    callDuration.value++
  }, 1000)
}

// 停止计时
const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
}

// 切换静音
const toggleMute = () => {
  isMuted.value = !isMuted.value
  // 这里实现静音逻辑
  showToast(isMuted.value ? '已静音' : '取消静音', 'info')
}

// 切换扬声器
const toggleSpeaker = () => {
  isSpeakerOn.value = !isSpeakerOn.value
  // 这里实现扬声器切换逻辑
  showToast(isSpeakerOn.value ? '扬声器开启' : '扬声器关闭', 'info')
}

// 更新音频质量
const updateAudioQuality = () => {
  // 这里可以根据实际的音频质量指标来更新
  // 暂时使用随机值模拟
  audioQuality.value = Math.floor(Math.random() * 5) + 1
}

// 获取质量文本
const getQualityText = (quality: number) => {
  const texts = ['很差', '较差', '一般', '良好', '优秀']
  return texts[quality - 1] || '未知'
}

// 获取用户状态文本
const getUserStatusText = (status: string) => {
  switch (status) {
    case UserStatus.ONLINE:
      return '在线'
    case UserStatus.TALKING:
      return '通话中'
    case UserStatus.BUSY:
      return '忙碌'
    default:
      return '离线'
  }
}

// 格式化通话时长
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 处理取消
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消通话吗？',
    success: (res) => {
      if (res.confirm) {
        cleanup()
        uni.navigateBack()
      }
    }
  })
}

// 清理资源
const cleanup = () => {
  // 停止录音
  if (audioRecorder) {
    audioRecorder.stop()
  }

  // 停止音频播放
  if (audioContext) {
    audioContext.stop()
    audioContext.destroy()
  }

  // 发送通话结束消息
  if (socketStore.isConnected) {
    sendCallMessage('voice-end', {
      sessionId: socketStore.currentCall?.callId,
      reason: 'user_hangup'
    })
  }

  // 清理定时器
  stopDurationTimer()

  // 更新用户状态
  userStore.updateUserStatus(UserStatus.ONLINE)
}

// 显示提示
const showToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  toastMessage.value = message
  toastType.value = type
  toastVisible.value = true
}
</script>

<style lang="scss" scoped>
.call-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.call-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  background: linear-gradient(180deg, #4a2c2a 0%, #2d1b1a 100%);
}

.status-bar {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
  padding-top: 50px; // 为状态栏留出空间

  .time {
    color: white;
    font-size: 16px;
    font-weight: 600;
  }
}

.main-content {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60px;

  .avatar-container {
    margin-bottom: 30px;

    .avatar-icon {
      width: 120px;
      height: 120px;
      background: #c62d42;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

      .avatar-emoji {
        font-size: 48px;
        color: white;
      }
    }
  }

  .username {
    font-size: 28px;
    font-weight: 400;
    color: white;
    text-align: center;
    letter-spacing: 1px;
  }
}

.status-section {
  margin-bottom: 80px;

  .status-text {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    letter-spacing: 0.5px;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
}

.bottom-controls {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 40px 60px;
  padding-bottom: 80px;

  .control-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;

    .button-circle {
      width: 70px;
      height: 70px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      // 次级按钮样式（灰色底+白色图标）
      &.secondary {
        background: rgba(128, 128, 128, 0.8);

        &.active {
          background: rgba(128, 128, 128, 1);
          transform: scale(1.05);
        }

        &:active {
          background: rgba(100, 100, 100, 0.9);
          transform: scale(0.95);
        }
      }

      // 主要按钮样式（红色底+白色图标）
      &.primary {
        background: #ff4757;

        &:active {
          background: #ff3742;
          transform: scale(0.95);
        }
      }

      .button-icon {
        font-size: 24px;

        &.white-icon {
          color: white !important;
          filter: brightness(0) invert(1);
        }
      }
    }

    .button-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      letter-spacing: 0.5px;
    }
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .status-bar {
    padding: 15px 20px;
    padding-top: 45px;
  }

  .avatar-section {
    .avatar-container .avatar-icon {
      width: 100px;
      height: 100px;

      .avatar-emoji {
        font-size: 40px;
      }
    }

    .username {
      font-size: 24px;
    }
  }

  .bottom-controls {
    padding: 30px 40px;
    padding-bottom: 60px;

    .control-button .button-circle {
      width: 60px;
      height: 60px;

      .button-icon {
        font-size: 20px;
      }
    }
  }
}
</style>
