/**
 * 清除除了指定用户以外的其他用户脚本
 */

const sqlite3 = require('sqlite3');
const { open } = require('sqlite');
const path = require('path');

// 数据库文件路径
const dbPath = path.join(__dirname, 'data/talking.db');

// 要保留的用户名
const keepUsername = '64597';

console.log('正在清除数据库中的用户...');
console.log('数据库路径:', dbPath);
console.log(`保留用户: ${keepUsername}`);

async function clearUsers() {
  let db;
  try {
    // 打开数据库连接
    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
    
    console.log('✅ 已连接到SQLite数据库');

    // 首先查询当前用户列表
    const allUsers = await db.all('SELECT id, username, status FROM users ORDER BY id');
    console.log(`\n📊 当前数据库中有 ${allUsers.length} 个用户:`);
    allUsers.forEach(user => {
      const statusEmoji = user.status === 'online' ? '🟢' : '⚫';
      console.log(`  ${statusEmoji} ID:${user.id} - ${user.username} (${user.status})`);
    });

    // 查找要保留的用户
    const keepUser = allUsers.find(user => user.username === keepUsername);
    if (!keepUser) {
      console.log(`❌ 未找到要保留的用户: ${keepUsername}`);
      return;
    }

    console.log(`\n✅ 找到要保留的用户: ID:${keepUser.id} - ${keepUser.username}`);

    // 查找要删除的用户
    const usersToDelete = allUsers.filter(user => user.username !== keepUsername);
    
    if (usersToDelete.length === 0) {
      console.log('✅ 没有需要删除的用户');
      return;
    }

    console.log(`\n🗑️ 将要删除 ${usersToDelete.length} 个用户:`);
    usersToDelete.forEach(user => {
      const statusEmoji = user.status === 'online' ? '🟢' : '⚫';
      console.log(`  ${statusEmoji} ID:${user.id} - ${user.username} (${user.status})`);
    });

    // 确认删除
    console.log('\n⚠️ 即将开始删除操作...');

    // 开始事务
    await db.exec('BEGIN TRANSACTION');

    try {
      // 删除相关的消息记录（可选，保持数据完整性）
      for (const user of usersToDelete) {
        // 删除用户发送的消息
        const deletedMessages = await db.run(
          'DELETE FROM messages WHERE from_user_id = ? OR to_user_id = ?',
          [user.id, user.id]
        );
        console.log(`  📧 删除用户 ${user.username} 的 ${deletedMessages.changes || 0} 条消息`);

        // 删除用户的会话记录
        const deletedSessions = await db.run(
          'DELETE FROM sessions WHERE user1_id = ? OR user2_id = ?',
          [user.id, user.id]
        );
        console.log(`  📞 删除用户 ${user.username} 的 ${deletedSessions.changes || 0} 个会话`);
      }

      // 删除用户记录
      const userIds = usersToDelete.map(user => user.id);
      const placeholders = userIds.map(() => '?').join(',');
      const deletedUsers = await db.run(
        `DELETE FROM users WHERE id IN (${placeholders})`,
        userIds
      );

      console.log(`\n✅ 成功删除 ${deletedUsers.changes || 0} 个用户`);

      // 提交事务
      await db.exec('COMMIT');
      console.log('✅ 事务提交成功');

    } catch (error) {
      // 回滚事务
      await db.exec('ROLLBACK');
      console.error('❌ 删除失败，事务已回滚:', error.message);
      throw error;
    }

    // 验证删除结果
    const remainingUsers = await db.all('SELECT id, username, status FROM users ORDER BY id');
    console.log(`\n📊 删除后数据库中剩余 ${remainingUsers.length} 个用户:`);
    remainingUsers.forEach(user => {
      const statusEmoji = user.status === 'online' ? '🟢' : '⚫';
      console.log(`  ${statusEmoji} ID:${user.id} - ${user.username} (${user.status})`);
    });

    console.log('\n🎉 用户清理完成！');
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  } finally {
    // 关闭数据库连接
    if (db) {
      await db.close();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

// 运行清理
clearUsers();
